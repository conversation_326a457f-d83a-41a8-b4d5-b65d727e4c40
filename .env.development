# 访问项目的根路径
VITE_PUBLIC_PATH = /

# 路由模式（true: history、false: hash）
VITE_ROUTE_WEB_HISTORY = false

# 代理设置，可配置多个，不能换行，格式：[访问接口的根路径, 代理地址, 是否保持Host头]
# VITE_PROXY = [["/WMS","http://************:8982/WMS",false]]
VITE_PROXY = [["/WMS","http://*************:8980/WMS",false]]
# VITE_PROXY = [["/WMS","http://************:8982/WMS",false]]
# 是否删除 console 调试信息
VITE_DROP_CONSOLE = false

# 访问接口的根路径（例如：https://vue.jeesite.com）建议为空
VITE_GLOB_API_URL =

# 访问接口的前缀，在根路径之后
VITE_GLOB_API_URL_PREFIX = /WMS

# 访问接口的管理基础路径
VITE_GLOB_ADMIN_PATH = /a

# 文件预览类型（true、oss）
VITE_FILE_PREVIEW = true
