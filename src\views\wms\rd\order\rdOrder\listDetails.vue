<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar> 
        <a-button type="default" @click="handleExport()">
          <Icon icon="i-ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ djno: record.parent.djno })">
          {{ record.parent.djno }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsWmsRdOrderRdOrderList">
  import { unref, onMounted,computed  } from 'vue';
  import { Table } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { rdOrderDelete, rdOrderSubListData } from '/@/api/wms/rd/order/rdOrder';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import { companyTreeData } from '/@/api/sys/company';
  import InputForm from './form.vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import { useUserStore } from '/@/store/modules/user';
  const userStore = useUserStore();

  const { t } = useI18n('wms.rd.order.rdOrder');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('出库订单明细管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('公司'),
        field: 'parent.companyCode',
        fieldLabel: 'parent.company.companyName',
        component: 'TreeSelect',
        componentProps: {
          api: companyTreeData,
          allowClear: true,
          immediate: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('出库单号'),
        field: 'parent.djno',
        component: 'Input',
      },
      {
        label: t('发货单号'),
        field: 'parent.fbillno',
        component: 'Input',
      },
      {
        label: t('拣货单号'),
        field: 'parent.pickCode',
        component: 'Input',
      },
      {
        label: t('订单号'),
        field: 'rdrecords32.forderno',
        component: 'Input',
      },
      {
        label: t('订单行号'),
        field: 'forderentryid',
        component: 'Input',
      },
      {
        label: t('发货日期'),
        field: 'parent.fdate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD',
          showTime: false,
          valueFormat: 'YYYY-MM-DD',
        },
      },
      {
        label: t('物料代码'),
        field: 'basInv.viewCode',
        component: 'Input',
      },
      {
        label: t('物料名称'),
        field: 'fitemname',
        component: 'Input',
      },
      {
        label: t('形象刊'),
        field: 'freeVO.cfree1',
        component: 'Input',
      },
      {
        label: t('联系人'),
        field: 'parent.fperson',
        component: 'Input',
      },
      {
        label: t('电话'),
        field: 'parent.fphone',
        component: 'Input',
      },
      {
        label: t('是否样书'),
        field: 'ifys',
        component: 'Input',
      },
      {
        label: t('物流单号'),
        field: 'parent.wlno',
        component: 'Input',
      },
      {
        label: t('物流公司'),
        field: 'parent.wlcompany',
        component: 'Input',
      },
      {
        label: t(''),
        field: 'parent.wlgs_NotNull',
        component: 'CheckboxGroup',
        componentProps: {
          // 数据源1：固定数据
          options: [
            { label: '物流公司为空', value: '1' },
          ],
        },
      },
      {
        label: t('物流件数'),
        field: 'parent.num',
        component: 'Input',
      },
      {
        label: t('备注信息'),
        field: 'parent.fexplanation',
        component: 'Input',
      },
      {
        label: t('物流日期'),
        field: 'parent.wldate',
        component: 'Input',
      },
      // {
      //   label: t('城市'),
      //   field: 'parent.fcity',
      //   component: 'Input',
      // },
      // {
      //   label: t('是否打印'),
      //   field: 'parent.fifprinteger',
      //   component: 'Input',
      // },
      {
        label: t('紧急程度'),
        field: 'parent.furgentlevel',
        component: 'Select',
        componentProps: {
          dictType: 'wms_jj_level',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('备货状态'),
        field: 'rdrecords32.bhStatus',
        component: 'Select',
        componentProps: {
          dictType: 'jh_bh_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('是否出库'),
        field: 'parent.status',
        component: 'Select',
        componentProps: {
          dictType: 'rd02_ck_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('是否推送'),
        field: 'parent.bsend',
        component: 'Select',
        componentProps: {
          dictType: 'sys_yes_no',
          allowClear: true,
        },
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('公司'),
      dataIndex: 'parent.company.companyName',
      // key: 'parent.company_name',
      // sorter: true,
      width: 160,
      align: 'left',
    },
    {
      title: t('出库单号'),
      dataIndex: 'parent.djno',
      key: 'parent.djno',
      sorter: true,
      width: 130,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('发货单号'),
      dataIndex: 'parent.fbillno',
      key: 'parent.fbillno',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('订单号'),
      dataIndex: 'rdrecords32.forderno',
      key: 'a.forderno',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('拣货单号'),
      dataIndex: 'parent.pickCode',
      key: 'parent.pickCode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('订单行号'),
      dataIndex: 'forderentryid',
      key: 'a.forderentryid',
      sorter: true,
      width: 90,
      align: 'left',
    },
    {
      title: t('发货日期'),
      dataIndex: 'parent.fdate',
      key: 'parent.fdate',
      sorter: true,
      width: 110,
      align: 'center',
    },
    {
      title: t('发运方式'),
      dataIndex: 'parent.fyfs',
      key: 'parent.fyfs',
      sorter: true,
      width: 130,
      align: 'left',
    },
    // {
    //   title: t('购货单位内码'),
    //   dataIndex: 'parent.fsupplyid',
    //   key: 'parent.fsupplyid',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('购货单位编码'),
    //   dataIndex: 'parent.fsupplierno',
    //   key: 'parent.fsupplierno',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    {
      title: t('购货单位名称'),
      dataIndex: 'parent.fsuppliername',
      key: 'parent.fsuppliername',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('联系人'),
      dataIndex: 'parent.fperson',
      key: 'parent.fperson',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('电话'),
      dataIndex: 'parent.fphone',
      key: 'parent.fphone',
      sorter: true,
      width: 130,
      align: 'left',
    },
    // {
    //   title: t('到站'),
    //   dataIndex: 'parent.fdz',
    //   key: 'parent.fdz',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('省份'),
    //   dataIndex: 'parent.fprovince',
    //   key: 'parent.fprovince',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('城市'),
    //   dataIndex: 'parent.fcity',
    //   key: 'parent.fcity',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('是否打印'),
    //   dataIndex: 'parent.fifprinteger',
    //   key: 'parent.fifprinteger',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    {
      title: t('紧急程度'),
      dataIndex: 'parent.furgentlevel',
      key: 'parent.furgentlevel',
      dictType: 'wms_jj_level',
      sorter: true,
      width: 120,
      align: 'left',
    },
    // {
    //   title: t('备注'),
    //   dataIndex: 'parent.fexplanation',
    //   key: 'parent.fexplanation',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('部门'),
    //   dataIndex: 'parent.fdepartment',
    //   key: 'parent.fdepartment',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('业务员'),
    //   dataIndex: 'parent.femp',
    //   key: 'parent.femp',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('主管'),
    //   dataIndex: 'parent.fempz',
    //   key: 'parent.fempz',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('制单'),
    //   dataIndex: 'parent.fbiller',
    //   key: 'parent.fbiller',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    {
      title: t('是否打包'),
      dataIndex: 'parent.fifpack',
      key: 'parent.fifpack',
      sorter: true,
      width: 130,
      align: 'left',
    },
    // {
    //   title: t('来源标识'),
    //   dataIndex: 'parent.sourceFlag',
    //   key: 'parent.source_flag',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('公司编码'),
    //   dataIndex: 'parent.companyCode',
    //   key: 'parent.company_code',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    {
      title: t('状态'),
      dataIndex: 'parent.status',
      key: 'parent.status',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'rd02_ck_status',
    },
    // {
    //   title: t('备注信息'),
    //   dataIndex: 'parent.remarks',
    //   key: 'parent.remarks',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('发货单行号'),
    //   dataIndex: 'fentryid',
    //   key: 'a.fentryid',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('订单号'),
    //   dataIndex: 'forderno',
    //   key: 'a.forderno',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('订单行号'),
    //   dataIndex: 'forderentryid',
    //   key: 'a.forderentryid',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('订单主表ID'),
    //   dataIndex: 'forderid',
    //   key: 'a.forderid',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('物料内码'),
    //   dataIndex: 'fitemid',
    //   key: 'a.fitemid',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    {
      title: t('物料代码'),
      dataIndex: 'basInv.viewCode',
      key: 'a.fitemno',
      sorter: true,
      width: 160,
      align: 'left',
    },
    {
      title: t('物料名称'),
      dataIndex: 'fitemname',
      key: 'a.fitemname',
      sorter: true,
      width: 160,
      align: 'left',
    },
    {
      title: t('辅助属性类别'),
      dataIndex: 'f109',
      key: 'a.f109',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('数量'),
      dataIndex: 'fqty',
      key: 'a.fqty',
      sorter: true,
      width: 100,
      align: 'right',
      showSummary: true,
      showTotalSummary: true,
    },
    {
      title: t('重量'),
      dataIndex: 'fweight',
      key: 'a.fweight',
      sorter: true,
      width: 100,
      align: 'right',
      showSummary: true,
      showTotalSummary: true,
    },
    {
      title: t('收货地址'),
      dataIndex: 'parent.faddress',
      key: 'parent.faddress',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('物流单号'),
      dataIndex: 'parent.wlno',
      key: 'parent.wlno',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('物流公司'),
      dataIndex: 'parent.wlcompany',
      key: 'parent.wlcompany',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('物流件数'),
      dataIndex: 'parent.num',
      key: 'parent.num',
      sorter: true,
      width: 90,
      align: 'left',
    },
    {
      title: t('物流日期'),
      dataIndex: 'parent.wldate',
      key: 'parent.wldate',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('打包单位'),
      dataIndex: 'fcustomerdb',
      key: 'a.fcustomerdb',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('收货单位'),
      dataIndex: 'fcustomersh',
      key: 'a.fcustomersh',
      sorter: true,
      width: 130,
      align: 'left',
    },
    // {
    //   title: t('收货联系人'),
    //   dataIndex: 'parent.fperson',
    //   key: 'parent.fperson',
    //   sorter: true,
    //   width: 100,
    //   align: 'left',
    // },
    // {
    //   title: t('电话'),
    //   dataIndex: 'parent.fphone',
    //   key: 'parent.fphone',
    //   sorter: true,
    //   width: 120,
    //   align: 'left',
    // },
    // {
    //   title: t('手机'),
    //   dataIndex: 'parent.fmobilephone',
    //   key: 'parent.fmobilephone',
    //   sorter: true,
    //   width: 120,
    //   align: 'left',
    // },
    
    // {
    //   title: t('备注信息'),
    //   dataIndex: 'fremarks',
    //   key: 'a.fremarks',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('仓库ID'),
    //   dataIndex: 'fstockid',
    //   key: 'a.fstockid',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('仓库编码'),
    //   dataIndex: 'fstockno',
    //   key: 'a.fstockno',
    //   sorter: true,
    //   width: 130,
    //   align: 'left',
    // },
    // {
    //   title: t('仓库'),
    //   dataIndex: 'fstockname',
    //   key: 'a.fstockname',
    //   sorter: true,
    //   width: 100,
    //   align: 'left',
    // },
    {
      title: t('品质'),
      dataIndex: 'fpz',
      key: 'a.fpz',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('要求到货日期'),
      dataIndex: 'freturndate',
      key: 'a.freturndate',
      sorter: true,
      width: 150,
      align: 'center',
    },
    {
      title: t('是否样书'),
      dataIndex: 'ifys',
      key: 'a.ifys',
      sorter: true,
      width: 100,
      align: 'left',
    },
    {
      title: t('累计出库数'),
      dataIndex: 'rdrecords32.sumCkQty',
      // key: 'a.sum_ck_qty',
      // sorter: true,
      width: 100,
      align: 'right',
    },
    {
      title: t('累计备货数'),
      dataIndex: 'rdrecords32.sumBhQty',
      // key: 'a.sum_bh_qty',
      // sorter: true,
      width: 100,
      align: 'right',
    },
    {
      title: t('累计拣货数'),
      dataIndex: 'rdrecords32.sumPickQty',
      // key: 'a.sum_pick_qty',
      // sorter: true,
      width: 100,
      align: 'right',
    },
    {
      title: t('备货状态'),
      dataIndex: 'rdrecords32.bhStatus',
      // key: 'a.bh_status',
      // sorter: true,
      width: 100,
      align: 'left',
      dictType: 'jh_bh_status',
    },
    {
      title: t('推送'),
      dataIndex: 'parent.bsend',
      key: 'a.bsend',
      // sorter: true,
      width: 100,
      align: 'center',
      dictType: 'sys_yes_no',
      // fixed: 'right',
    },
    {
      title: t('形象刊'),
      dataIndex: 'freeVO.cfree1',
      // key: 'a.cfree1',
      // sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备注信息'),
      dataIndex: 'parent.fexplanation',
      key: 'parent.fexplanation',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑出库订单'),
        onClick: handleForm.bind(this, { djno: record.djno }),
        auth: 'wms:rd:order:rdOrder:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除出库订单'),
        popConfirm: {
          title: t('是否确认删除出库订单'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'wms:rd:order:rdOrder:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload, getForm, getDataSource, setProps }] = useTable({
    api: rdOrderSubListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    //actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
    immediate: false,
    showCustomSummary: true,
    showTotalCustomSummary: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/wms/rd/order/rdOrder/exportSubData',
      params: getForm().getFieldsValue(),
    });
  }

  async function handleDelete(record: Recordable) {
    const params = { djno: record.djno };
    const res = await rdOrderDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }

  onMounted(async () => {
    setProps()
    handleCompany();
    getHomePage();
  });

  async function handleCompany() {
    await getForm().setFieldsValue({
      'parent.companyCode': userStore.getProjecte.code,
    });
    reload();
    await getForm().setFieldsValue({
      'parent.companyCode': userStore.getProjecte.code,
    });
  }

  async function getHomePage() {
    if(router.currentRoute.value.query.homePage == 'true'){
      await getForm().setFieldsValue({
        status: '0',
      });
      reload();
    }
  }

  const totalFqty = computed(() => {
    const tableData = getDataSource();
    // const tableData = getDataSource.value;
    let sum = tableData.reduce((total, item) => total + (item.fqty || 0), 0);
    return sum;
  });

  const totalFweight = computed(() => {
    const tableData = getDataSource();
    // const tableData = getDataSource.value;
    let sum = tableData.reduce((total, item) => total + (item.fweight || 0), 0);
    sum = sum.toFixed(2)
    return sum;
  });
</script>
