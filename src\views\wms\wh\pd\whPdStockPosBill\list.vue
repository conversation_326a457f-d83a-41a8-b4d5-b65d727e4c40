<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <!-- <a-button type="primary" @click="handleForm({})" v-auth="'wh:pd:whPdStockBill:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button> -->
        <a-button type="primary" @click="handleForm({})" v-auth="'wh:pd:whPdStockPosBill:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
        <a-button type="primary" @click="handleFormWh({})" v-auth="'wh:pd:whPdStock:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('按仓库/货位盘点') }}
        </a-button>
        <a-button type="default" @click="handleExport()">
          <Icon icon="i-ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
        <a-button
          type="dashed"
          success
          v-auth="'wh:pd:whPdStock:edit'"
          @click="handleGenerateTask2({})"
        >
          <Icon icon="i-ant-design:check-outlined" /> {{ t('生成盘盈盘亏') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleFormWh({ djno: record.djno })">
          {{ record.djno }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <FormWh @register="registerDrawerWh" @success="handleSuccessWh" />
  </div>
</template>
<script lang="ts" setup name="ViewsWhPdWhPdStockPosBillList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { whPdStockPosBillDelete, whPdStockPosBillListData } from '/@/api/wms/wh/pd/whPdStockPosBill';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';
  import FormWh from '/@/views/wms/wh/pd/whPdStockPosBill/formWh.vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';

  const { t } = useI18n('wh.pd.whPdStockPosBill');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('盘点管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('盘点方式'),
        field: 'pdType',
        component: 'Input',
      },
      {
        label: t('单据日期'),
        field: 'ddate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('盘点状态'),
        field: 'pdStatus',
        component: 'Input',
      },
      {
        label: t('总数'),
        field: 'iqty',
        component: 'Input',
      },
      {
        label: t('实盘总数'),
        field: 'sumqty',
        component: 'Input',
      },
      {
        label: t('仓库'),
        field: 'whcode',
        component: 'Input',
      },
      {
        label: t('货位'),
        field: 'posCode',
        component: 'Input',
      },
      {
        label: t('公司编码'),
        field: 'companyCode',
        component: 'Input',
      },
      {
        label: t('公司名称'),
        field: 'companyname',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('单据号'),
      dataIndex: 'djno',
      key: 'a.djno',
      sorter: true,
      width: 130,
      align: 'center',
      slot: 'firstColumn',
    },
    {
      title: t('盘点方式'),
      dataIndex: 'pdType',
      key: 'a.pd_type',
      sorter: true,
      width: 230,
      align: 'left',
      dictType: 'wms_pd_type',
      // slot: 'firstColumn',
    },
    {
      title: t('单据日期'),
      dataIndex: 'ddate',
      key: 'a.ddate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('盘点状态'),
      dataIndex: 'pdStatus',
      key: 'a.pd_status',
      dictType: 'wms_pd_status',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('总数'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('实盘总数'),
      dataIndex: 'sumqty',
      key: 'a.sumqty',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('仓库'),
      dataIndex: 'whcode',
      key: 'a.whcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('货位'),
      dataIndex: 'posCode',
      key: 'a.pos_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('公司编码'),
      dataIndex: 'companyCode',
      key: 'a.company_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('公司名称'),
      dataIndex: 'company.companyName',
      key: 'a.companyname',
      // sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑盘点'),
        onClick: handleFormWh.bind(this, { djno: record.djno }),
        auth: 'wh:pd:whPdStockPosBill:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除盘点'),
        popConfirm: {
          title: t('是否确认删除盘点'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'wh:pd:whPdStockPosBill:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerDrawerWh, { openDrawer: openDrawerWh }] = useDrawer();
  const [registerTable, { reload, getForm, getSelectRowKeys }] = useTable({
    api: whPdStockPosBillListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { djno: record.djno };
    const res = await whPdStockPosBillDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }

  function handleFormWh(record: Recordable) {
    openDrawerWh(true, record);
  }

  async function handleGenerateTask2() {
    let arr = await getSelectRowKeys();
    if (getSelectRowKeys().length != 1) {
      showMessage(t('请选择一行数据'));
      return;
    }
    const selIds = arr.join(',');
    const params = { djno: selIds };
    // todo 生成任务API
    // const res = await createRdBill(params);
    // console.log(res, 'res');
    // showMessage(res.message);
    // handleSuccess({});
  }

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/wh/pd/whPdStockBill/exportData',
      params: getForm().getFieldsValue(),
    });
  }
</script>
