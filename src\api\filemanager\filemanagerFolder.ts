/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface FilemanagerFolder extends TreeModel<FilemanagerFolder> {
  folderName?: string; // 文件夹名
  groupType?: string; // 文件分组类型
  officeCode?: string; // 部门编码
}

export const filemanagerFolderList = (params?: FilemanagerFolder | any) =>
  defHttp.get<FilemanagerFolder>({
    url: adminPath + '/filemanager/filemanagerFolder/list',
    params,
  });

export const filemanagerFolderListData = (params?: FilemanagerFolder | any) =>
  defHttp.post<FilemanagerFolder[]>({
    url: adminPath + '/filemanager/filemanagerFolder/listData',
    params,
  });

export const filemanagerFolderForm = (params?: FilemanagerFolder | any) =>
  defHttp.get<FilemanagerFolder>({
    url: adminPath + '/filemanager/filemanagerFolder/form',
    params,
  });

export const filemanagerFolderCreateNextNode = (params?: FilemanagerFolder | any) =>
  defHttp.get<FilemanagerFolder>({
    url: adminPath + '/filemanager/filemanagerFolder/createNextNode',
    params,
  });

export const filemanagerFolderSave = (params?: any, data?: FilemanagerFolder | any) =>
  defHttp.postJson<FilemanagerFolder>({
    url: adminPath + '/filemanager/filemanagerFolder/save',
    params,
    data,
  });

export const filemanagerFolderDelete = (params?: FilemanagerFolder | any) =>
  defHttp.get<FilemanagerFolder>({
    url: adminPath + '/filemanager/filemanagerFolder/delete',
    params,
  });

export const filemanagerFolderTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({
    url: adminPath + '/filemanager/filemanagerFolder/treeData',
    params,
  });
