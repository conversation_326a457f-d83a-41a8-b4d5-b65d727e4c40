/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherViewListQueryCol extends BasicModel<LayVoucherViewListQueryCol> {
  queryCode?: string; // 查询标志
  viewCode?: string; // 布局标志
  vouchCode?: string; // 基础单据
  schLable?: string; // 标签名称
  schFiled?: string; // 字段名称
  schComponent?: string; // 组件类型
  schDefaultValue?: string; // 默认值
  schIsShow?: string; // 是否显示
  schDynamicDisable?: string; // 是否禁用
  compAllowClear?: string; // 是否允许删除
  compMode?: string; // 是否启用多选
  compDictType?: string; // 参数字典
  compFormat?: string; // 日期格式
  compShowTime?: string; // 是否显示时分秒
  compApi?: string; // 方法名称
  compParams?: string; // 参数地址
  compMethods?: string; // 事件函数
}

export const layVoucherViewListQueryColList = (params?: LayVoucherViewListQueryCol | any) =>
  defHttp.get<LayVoucherViewListQueryCol>({ url: adminPath + '/layout/listQueryCol/list', params });

export const layVoucherViewListQueryColListData = (params?: LayVoucherViewListQueryCol | any) =>
  defHttp.post<Page<LayVoucherViewListQueryCol>>({ url: adminPath + '/layout/listQueryCol/listData', params });

export const layVoucherViewListQueryColForm = (params?: LayVoucherViewListQueryCol | any) =>
  defHttp.get<LayVoucherViewListQueryCol>({ url: adminPath + '/layout/listQueryCol/form', params });

export const layVoucherViewListQueryColSave = (params?: any, data?: LayVoucherViewListQueryCol | any) =>
  defHttp.postJson<LayVoucherViewListQueryCol>({ url: adminPath + '/layout/listQueryCol/save', params, data });

export const layVoucherViewListQueryColDelete = (params?: LayVoucherViewListQueryCol | any) =>
  defHttp.get<LayVoucherViewListQueryCol>({ url: adminPath + '/layout/listQueryCol/delete', params });
