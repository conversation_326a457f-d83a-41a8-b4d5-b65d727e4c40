<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="default" @click="handleExport()">
          <Icon icon="i-ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts" setup name="ViewsWmsWhPdWhPdStockPosBillListInvDetail">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { whPdStockPosBillsInvListData } from '/@/api/wms/wh/pd/whPdStockPosBillsInv';
  import { FormProps } from '/@/components/Form';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';

  const { t } = useI18n('wh.pd.whPdStockPosBill');
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('盘点管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('公司名称'),
        field: 'parent.djno.company.companyName',
        component: 'Input',
      },
      {
        label: t('盘点单号'),
        field: 'parent.djno.djno',
        component: 'Input',
      },
      {
        label: t('单据日期'),
        field: 'parent.djno.ddate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD 00:00:00',
        },
      },
      {
        label: t('盘点方式'),
        field: 'pdType',
        component: 'Select',
        componentProps: {
          dictType: 'wms_pd_type',
          allowClear: true,
        },
      },
      {
        label: t('仓库'),
        field: 'parent.djno.basWare.cwhname',
        component: 'Input',
      },
      {
        label: t('货位'),
        field: 'parent.basPosition.posName',
        component: 'Input',
      },
      {
        label: t('商品编码'),
        field: 'basInv.viewCode',
        component: 'Input',
      },
      {
        label: t('商品名称'),
        field: 'basInv.viewName',
        component: 'Input',
      },
      {
        label: t('形象刊'),
        field: 'cfree1',
        component: 'Input',
      },
      {
        label: t('自由项2'),
        field: 'cfree2',
        component: 'Input',
      },
      {
        label: t('自由项3'),
        field: 'cfree3',
        component: 'Input',
      },
      {
        label: t('制单人'),
        field: 'parent.djno.createByName',
        component: 'Input',
      },
      {
        label: t('盘点状态'),
        field: 'parent.djno.pdStatus',
        component: 'Select',
        componentProps: {
          dictType: 'wms_pd_status',
          allowClear: true,
        },
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('公司'),
      dataIndex: 'parent.djno.company.companyName',
      key: 'company.company_code',
      sorter: true,
      width: 230,
      align: 'left',
    },
    {
      title: t('盘点单号'),
      dataIndex: 'parent.djno.djno',
      key: 'djno.djno',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('单据日期'),
      dataIndex: 'parent.djno.ddate',
      key: 'djno.ddate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('盘点方式'),
      dataIndex: 'parent.djno.pdType',
      key: 'djno.pd_type',
      sorter: true,
      width: 110,
      align: 'left',
      dictType: 'wms_pd_type',
    },
    {
      title: t('总数'),
      dataIndex: 'parent.djno.iqty',
      key: 'djno.iqty',
      sorter: true,
      width: 110,
      align: 'right',
    },
    {
      title: t('实盘总数'),
      dataIndex: 'parent.djno.sumqty',
      key: 'djno.sumqty',
      sorter: true,
      width: 110,
      align: 'right',
    },
    {
      title: t('仓库'),
      dataIndex: 'parent.djno.basWare.cwhname',
      key: 'house.whcode',
      sorter: true,
      width: 90,
      align: 'left',
    },
    {
      title: t('商品编码'),
      dataIndex: 'basInv.viewCode',
      key: 'inv.inv_code',
      sorter: true,
      width: 150,
      align: 'left',
    },
    {
      title: t('商品名称'),
      dataIndex: 'basInv.invName',
      key: 'inv.inv_name',
      sorter: true,
      width: 270,
      align: 'left',
    },
    {
      title: t('单位'),
      dataIndex: 'basInv.funitname',
      key: 'inv.funitname',
      sorter: true,
      width: 80,
      align: 'left',
    },
    {
      title: t('货位'),
      dataIndex: 'parent.basPosition.posName',
      key: 'position.pos_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('账存数'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('实盘数'),
      dataIndex: 'frealqty',
      key: 'a.frealqty',
      sorter: true,
      width: 130,
      align: 'right',
    },
    {
      title: t('形象刊'),
      dataIndex: 'cfree1',
      key: 'a.cfree1',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('自由项2'),
      dataIndex: 'cfree2',
      key: 'a.cfree2',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('自由项3'),
      dataIndex: 'cfree3',
      key: 'a.cfree3',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('制单时间'),
      dataIndex: 'parent.djno.createDate',
      key: 'djno.create_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('制单人'),
      dataIndex: 'parent.djno.createByName',
      key: 'djno.create_by_name',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('备注'),
      dataIndex: 'parent.djno.remarks',
      key: 'djno.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },

    {
      title: t('盘点状态'),
      dataIndex: 'parent.djno.pdStatus',
      key: 'djno.pd_status',
      sorter: true,
      width: 100,
      align: 'center',
      dictType: 'wms_pd_status',
    },
  ];

  const [registerTable, { getForm }] = useTable({
    api: whPdStockPosBillsInvListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/wh/pd/whPdStockPosBillsInv/exportSubData',
      params: getForm().getFieldsValue(),
    });
  }
</script>
