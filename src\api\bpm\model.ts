/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { Page, TreeModel } from '../model/baseModel';

export interface BaseBpmModel<T> extends Recordable {
  id?: string;
  page?: Page<T>;
  beginDate?: string;
  endDate?: string;
  status?: string; // 流程状态 (1 为完成 2 已完成);
  params?: Recordable; // 流程信息返回参数
}

export interface BpmForm extends TreeModel<BpmForm> {
  optionMap: Recordable;
}

export interface BpmProcDef extends BaseBpmModel<BpmProcDef> {
  name?: string;
  description?: string;
  key?: string;
  category?: string;
  version?: number;
  deploymentId?: string;
  tenantId?: string;
  form?: BpmForm; // 获取流程或任务的时候得到的表单信息
}

export interface BpmProcIns extends BaseBpmModel<BpmProcDef> {
  name?: string;
  businessKey?: string; // 业务Key，组成：formKey:bizKey
  procDef?: BpmProcDef;
  tenantId?: string;
  startTime?: string;
  endTime?: string;
  startUserId?: string;
  startUserName?: string;
  deleteReason?: string;
  variables?: Map<string, any>;
  formKey?: string;
  bizKey?: string;
}

export interface BpmTask extends BaseBpmModel<BpmProcDef> {
  name?: string;
  description?: string;
  category?: string;
  assignee?: string;
  procIns?: BpmProcIns;
  createTime?: string;
  claimTime?: string;
  dueDate?: string;
  endTime?: string;
  duration?: number;
  deleteReason?: string;
  priority?: number;
  executionId?: string;
  scopeId?: string;
  scopeType?: string;
  scopeDefinitionId?: string;
  parentTaskId?: string;

  hasMultiInstance?: boolean;

  variables?: Map<string, any>; // 流程变量
  transientVariables?: Map<string, any>; // 瞬态流程变量

  delegateState?: string; // 委托状态 PENDING、RESOLVED；设置true，则代表是委托任务
  assigneeCode?: string; // 处理人编码（包含候选用户、候选角色的编码）列表上用
  assigneeInfo?: string; // 处理人信息（包含候选用户、候选角色的名称）列表上用

  comment?: string; // 任务提交意见
  activityId?: string; // 活动编号；退回任务使用，退回的节点编码
  nextUserCodes?: string; // 下一步处理人用户编码

  extendMap?: Map<string, any>; // 扩展字段Map
}

export interface BpmParams extends Recordable {
  taskId?: string; // 任务编号
  procInsId?: string; // 实例编号
  activityId?: string; // 活动编号

  comment?: string; // 审批意见

  dueDate?: string; // 任务期限
  priority?: number; // 任务优先级
  nextUserCodes?: string; // 下一步处理人用户编码

  isView?: boolean; // 是否为查看表单（来自：已办、我创建、查询等）
}
