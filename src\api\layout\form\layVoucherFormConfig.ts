/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherFormConfig extends BasicModel<LayVoucherFormConfig> {
  viewCode?: string; // 布局标志
  isBpmForm?: string; // 是否流程表单
  bpmFormKey?: string; // 流程表单Key
  formColNum?: string; // 表单布局
  formTitle?: string; // 表单标题
}

export const layVoucherFormConfigList = (params?: LayVoucherFormConfig | any) =>
  defHttp.get<LayVoucherFormConfig>({
    url: adminPath + '/layout/formConfig/list',
    params,
  });

export const layVoucherFormConfigListData = (params?: LayVoucherFormConfig | any) =>
  defHttp.post<Page<LayVoucherFormConfig>>({
    url: adminPath + '/layout/formConfig/listData',
    params,
  });

export const layVoucherFormConfigForm = (params?: LayVoucherFormConfig | any) =>
  defHttp.get<LayVoucherFormConfig>({
    url: adminPath + '/layout/formConfig/form',
    params,
  });

export const layVoucherFormConfigSave = (params?: any, data?: LayVoucherFormConfig | any) =>
  defHttp.postJson<LayVoucherFormConfig>({
    url: adminPath + '/layout/formConfig/save',
    params,
    data,
  });

export const layVoucherFormConfigDelete = (params?: LayVoucherFormConfig | any) =>
  defHttp.get<LayVoucherFormConfig>({
    url: adminPath + '/layout/formConfig/delete',
    params,
  });

export const layVoucherFormConfigFindOne = (params?: LayVoucherFormConfig | any) =>
  defHttp.get<LayVoucherFormConfig>({
    url: adminPath + '/layout/formConfig/findOne',
    params,
  });
