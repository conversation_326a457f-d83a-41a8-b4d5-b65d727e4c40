/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherFormTabConfig extends BasicModel<LayVoucherFormTabConfig> {
  viewCode?: string; // 布局标志
  tabName?: string; // 标签页名称
  tabAddress?: string; // 标签页地址
  sort?: number; // 顺序号
}

export const layVoucherFormTabConfigList = (params?: LayVoucherFormTabConfig | any) =>
  defHttp.get<LayVoucherFormTabConfig>({ url: adminPath + '/layout/formTab/list', params });

export const layVoucherFormTabConfigListData = (params?: LayVoucherFormTabConfig | any) =>
  defHttp.post<Page<LayVoucherFormTabConfig>>({ url: adminPath + '/layout/formTab/listData', params });

export const layVoucherFormTabConfigForm = (params?: LayVoucherFormTabConfig | any) =>
  defHttp.get<LayVoucherFormTabConfig>({ url: adminPath + '/layout/formTab/form', params });

export const layVoucherFormTabConfigSave = (params?: any, data?: LayVoucherFormTabConfig | any) =>
  defHttp.postJson<LayVoucherFormTabConfig>({ url: adminPath + '/layout/formTab/save', params, data });

export const layVoucherFormTabConfigDelete = (params?: LayVoucherFormTabConfig | any) =>
  defHttp.get<LayVoucherFormTabConfig>({ url: adminPath + '/layout/formTab/delete', params });
