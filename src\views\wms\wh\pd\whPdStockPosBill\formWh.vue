<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'wh:pd:whPdStock:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm">
      <template #queryButton>
        <div class="text-right">
          <a-button type="primary" :loading="queryLoading" @click="handleQuery">
            {{ t('查询') }}
          </a-button>
        </div>
      </template>
    </BasicForm>

    <!-- 查询结果表格 v-if="showQueryTable"-->
    <div class="mt-4">
      <h3 class="mb-2">{{ t('盘点明细') }}</h3>
      <BasicTable
        @register="registerWhPdStocksTable"
        :canResize="false"
        :showTableSetting="false"
      />
    </div>
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsWhPdWhPdStockForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { WhPdStockPosBill, whPdStockPosBillForm, whPdStockPosBillSave, saveByType, whPdStockPosBillsListData } from '/@/api/wms/wh/pd/whPdStockPosBill';
  import { companyTreeData } from '/@/api/sys/company';
  import { useUserStore } from '/@/store/modules/user';
  import { basWarehouseTreeData } from '/@/api/bas/house/basWarehouse';
  import { basPositionTreeData } from '/@/api/bas/pos/basPosition';
  import { useDict } from '/@/components/Dict';
import { on } from '../../../../../utils/domUtils';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('wh.pd.whPdStockPosBill');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<WhPdStockPosBill>({} as WhPdStockPosBill);
  const showQueryTable = ref(false);
  const userStore = useUserStore();
  const pdTypeOptions = ref<any[]>([]);
  const queryLoading = ref(false);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: (record.value as any).isNewRecord ? t('新增盘点单') : t('编辑盘点单'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('单据日期'),
      field: 'ddate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        // showTime: { format: 'HH:mm' },
      },
      required: true,
    },
    {
      label: t('单据号'),
      field: 'djno',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
      dynamicDisabled: true,
    },
    // {
    //   label: t('备注'),
    //   field: 'fexplanation',
    //   component: 'Input',
    //   componentProps: {
    //     maxlength: 255,
    //   },
    // },
    {
      label: t('总数'),
      field: 'iqty',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      dynamicDisabled: true,
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('实盘总数'),
      field: 'sumQty',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      dynamicDisabled: true,
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('公司名称'),
      field: 'companyCode',
      fieldLabel: 'company.companyName',
      component: 'TreeSelect',
      componentProps: {
        api: companyTreeData,
        allowClear: true,
        immediate: true,
      },
      required: true,
    },
    {
      label: t('公司编码'),
      field: 'companyCode',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
      dynamicDisabled: true,
    },
    {
      label: t('盘点方式'),
      field: 'pdType',
      component: 'Select',
      componentProps: {
        maxlength: 64,
        allowClear: true,
        options: pdTypeOptions,
        onChange: (value: string) => {
          // 当盘点方式为1时，货位字段为必填
          updateSchema([
            {
              field: 'posCode',
              required: value === '1',
            },
          ]);
        },
      },
      defaultValue: '2',
      required: true,
    },
    {
      label: t('仓库'),
      field: 'whcode',
      fieldLabel: 'basWare.cwhname',
      component: 'TreeSelect',
      componentProps: {
        api: basWarehouseTreeData,
        allowClear: true,
      },
      required: true,
    },
    {
      label: t('货位'),
      field: 'posCode',
      component: 'TreeSelect',
      componentProps: {
        api: basPositionTreeData,
        // params: { whcode: whCode.value },
        allowClear: true,
        canSelectParent: false,
        treeCheckable: true,
        immediate: true,
      },
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      label: '',
      field: 'queryButton',
      component: 'Input',
      colProps: { lg: 24, md: 24 },
      slot: 'queryButton',
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerWhPdStocksTable, whPdStocksTable] = useTable({
    actionColumn: {
      width: 60,
      actions: (record: Recordable) => [
        {
          label: '明细',
          // auth: 'wh:pd:whPdStock:edit',
        },
      ],
    },
    rowKey: 'id',
    pagination: {
      pageSize: 10,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    },
    bordered: true,
    size: 'small',
    inset: true,
    showCustomSummary: true,
    showTotalCustomSummary: true,
    onChange: handleTableChange,
  });

  async function setWhPdStocksTableData(_res: Recordable) {
    whPdStocksTable.setColumns([
      {
        title: t('单据号'),
        dataIndex: 'djno.djno',
        width: 130,
        align: 'left',
      },
      {
        title: t('货位'),
        dataIndex: 'posCode',
        width: 130,
        align: 'left',
      },
      {
        title: t('货位名称'),
        dataIndex: 'basPosition.posName',
        width: 130,
        align: 'left',
      },
      {
        title: t('账存数'),
        dataIndex: 'iqty',
        width: 130,
        align: 'left',
        showSummary: true,
        showTotalSummary: true,
      },
      {
        title: t('实盘数'),
        dataIndex: 'frealqty',
        width: 130,
        align: 'left',
        showSummary: true,
        showTotalSummary: true,
      },
      {
        title: t('盘点状态'),
        dataIndex: 'pdStatus',
        dictType: 'wms_pd_pdStatus',
        width: 130,
        align: 'left',
      },
      {
        title: t('行备注'),
        dataIndex: 'remarks',
        width: 130,
        align: 'left',
      },
    ]);
    // whPdStocksTable.setTableData(record.value.whPdStocksList || []);
    // 如果存在record.value.id，说明是编辑模式
    if (record?.value?.id) {
      // 调用接口subListData
      const res = await whPdStockPosBillsListData({
        djno: record.value?.djno,
        pageNo: 1,
        pageSize: 10,
      });
      // 把 res 赋值给 whPdStocksTable，whPdStocksTable表分页显示
      whPdStocksTable.setTableData(res.list || []);
      // 分页
      whPdStocksTable.setPagination({
        total: res.count,
        current: 1,
        pageSize: 10,
      });
    }
  }

  function handleWhPdStocksRowClick(record: Recordable) {
    record.onEdit?.(true, false);
  }

  // 处理表格分页、排序、筛选变化
  async function handleTableChange(pagination: any) {
    try {
      const res = await whPdStockPosBillsListData({
        djno: record.value?.djno,
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
      });

      whPdStocksTable.setTableData(res.list || []);
      whPdStocksTable.setPagination({
        total: res.count,
        current: pagination.current,
        pageSize: pagination.pageSize,
      });
    } catch (error) {
      console.log('分页查询错误:', error);
    }
  }

  // 查询函数
  async function handleQuery() {
    try {
      queryLoading.value = true;
      // 验证表单
      const formData = await validate();
      console.log('表单验证通过，查询参数:', formData);

      // 删除basWare.cwhname 和 company.companyName
      delete formData.basWare.cwhname;
      delete formData.company.companyName;

      // 调用接口saveByPdType
      const resPdType = await saveByType(formData);

      if (resPdType.result === 'true') {
        // 查询
        const res = await whPdStockPosBillsListData({
          djno: record.value?.djno,
          pageNo: 1,
          pageSize: 10,
        });
        // 把 res 赋值给 whPdStocksTable，whPdStocksTable表分页显示
        whPdStocksTable.setTableData(res.list || []);
        // 分页
        whPdStocksTable.setPagination({
          total: res.count,
          current: 1,
          pageSize: 10,
        });
        // whPdStocksTable.setTableData(res.whPdStocksList || []);
      }
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('查询错误:', error);
    } finally {
      queryLoading.value = false;
    }
  }

  function handleWhPdStocksAdd() {
    whPdStocksTable.insertTableDataRecord({
      id: new Date().getTime(),
      isNewRecord: true,
      editable: true,
    });
  }

  function handleWhPdStocksDelete(record: Recordable) {
    whPdStocksTable.deleteTableDataRecord(record);
  }

  async function getWhPdStocksList() {
    let whPdStocksListValid = true;
    let whPdStocksList: Recordable[] = [];
    for (const record of whPdStocksTable.getDataSource()) {
      if (!(await record.onEdit?.(false, true))) {
        whPdStocksListValid = false;
      }
      whPdStocksList.push({
        ...record,
        id: !!record.isNewRecord ? '' : record.id,
      });
    }
    for (const record of whPdStocksTable.getDelDataSource()) {
      if (!!record.isNewRecord) continue;
      whPdStocksList.push({
        ...record,
        status: '1',
      });
    }
    if (!whPdStocksListValid) {
      throw { errorFields: [{ name: ['whPdStocksList'] }] };
    }
    return whPdStocksList;
  }

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();

    // 加载盘点方式字典选项，并禁用值为0的选项
    const dictList = await useDict().initGetDictList('wms_pd_type');
    pdTypeOptions.value = dictList.map((item) => ({
      label: item.name,
      value: item.value,
      disabled: item.value === '0',
    }));

    const res = await whPdStockPosBillForm(data);
    record.value = (res.whPdStockPosBill || {}) as WhPdStockPosBill;
    record.value.__t = new Date().getTime();

    // 如果是新建记录且没有设置单据日期，则设置为当天
    if ((record.value as any).isNewRecord && !record.value.ddate) {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      // const hours = String(now.getHours()).padStart(2, '0');
      // const minutes = String(now.getMinutes()).padStart(2, '0');
      record.value.ddate = `${year}-${month}-${day}`;
    }

    // 从userStore获取公司编码
    record.value.companyCode = record.value.companyCode ? record.value.companyCode : userStore.getProjecte.code;
    record.value.companyName = record.value.companyName ? record.value.companyName : userStore.getProjecte.name;

    // 如果是新建记录且没有设置盘点方式，则设置默认值为2
    if ((record.value as any).isNewRecord && !record.value.pdType) {
      record.value.pdType = '2';
    }

    setFieldsValue(record.value);

    // 根据当前的盘点方式值设置货位字段的必填状态
    updateSchema([
      {
        field: 'posCode',
        required: record.value.pdType === '1',
      },
    ]);

    setWhPdStocksTableData(res);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: (record.value as any).isNewRecord,
        id: (record.value as any).id,
      };
      data.whPdStocksList = await getWhPdStocksList();
      // console.log('submit', params, data, record);
      const res = await whPdStockPosBillSave(params, data);
      showMessage((res as any).message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
