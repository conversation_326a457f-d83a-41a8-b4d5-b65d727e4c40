/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhPdStockPosBill extends BasicModel<WhPdStockPosBill> {
  djno?: string; // 单据号
  pdType?: string; // 盘点方式
  ddate?: string; // 单据日期
  pdStatus?: string; // 盘点状态
  iqty?: number; // 总数
  sumqty?: number; // 实盘总数
  whcode?: string; // 仓库
  posCode?: string; // 货位
  companyCode?: string; // 公司编码
  companyname?: string; // 公司名称
  whPdStockPosBillsList?: any[]; // 子表列表
}

export const whPdStockPosBillList = (params?: WhPdStockPosBill | any) =>
  defHttp.get<WhPdStockPosBill>({ url: adminPath + '/wh/pd/whPdStockPosBill/list', params });

export const whPdStockPosBillListData = (params?: WhPdStockPosBill | any) =>
  defHttp.post<Page<WhPdStockPosBill>>({ url: adminPath + '/wh/pd/whPdStockPosBill/listData', params });

export const whPdStockPosBillForm = (params?: WhPdStockPosBill | any) =>
  defHttp.get<WhPdStockPosBill>({ url: adminPath + '/wh/pd/whPdStockPosBill/form', params });

export const whPdStockPosBillSave = (params?: any, data?: WhPdStockPosBill | any) =>
  defHttp.postJson<WhPdStockPosBill>({ url: adminPath + '/wh/pd/whPdStockPosBill/save', params, data });

export const whPdStockPosBillDelete = (params?: WhPdStockPosBill | any) =>
  defHttp.get<WhPdStockPosBill>({ url: adminPath + '/wh/pd/whPdStockPosBill/delete', params });

// /wh/pd/whPdStockPosBill/saveByType
export const saveByType = (params?: WhPdStockPosBill | any) =>
  defHttp.post<Page<WhPdStockPosBill>>({ url: adminPath + '/wh/pd/whPdStockPosBill/saveByType', params });

export const whPdStockPosBillsListData = (params?: WhPdStockPosBill | any) =>
  defHttp.post<Page<WhPdStockPosBill>>({ url: adminPath + '/wh/pd/whPdStockPosBill/whPdStockPosBillsListData', params });
