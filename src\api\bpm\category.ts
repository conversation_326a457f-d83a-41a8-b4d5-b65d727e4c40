/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export const bpmCategoryTreeData = (params?: any) =>
  defHttp.post<TreeDataModel[]>({ url: adminPath + '/bpm/bpmCategory/treeData', params });
