<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'wh:pd:whPdStockPosBillsInv:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ id: record.id })">
          {{ record.hid }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsWhPdWhPdStockPosBillsInvList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { whPdStockPosBillsInvDelete, whPdStockPosBillsInvListData } from '/@/api/wms/wh/pd/whPdStockPosBillsInv';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('wh.pd.whPdStockPosBillsInv');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('盘点商品明细管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      {
        label: t('hid'),
        field: 'hid',
        component: 'Input',
      },
      {
        label: t('单据号'),
        field: 'djno',
        component: 'Input',
      },
      {
        label: t('账存数'),
        field: 'iqty',
        component: 'Input',
      },
      {
        label: t('实盘数'),
        field: 'frealqty',
        component: 'Input',
      },
      {
        label: t('物料编码'),
        field: 'cinvcode',
        component: 'Input',
      },
      {
        label: t('自由项1'),
        field: 'cfree1',
        component: 'Input',
      },
      {
        label: t('自由项2'),
        field: 'cfree2',
        component: 'Input',
      },
      {
        label: t('自由项3'),
        field: 'cfree3',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('hid'),
      dataIndex: 'hid',
      key: 'a.hid',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('单据号'),
      dataIndex: 'djno',
      key: 'a.djno',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('账存数'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('实盘数'),
      dataIndex: 'frealqty',
      key: 'a.frealqty',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('物料编码'),
      dataIndex: 'cinvcode',
      key: 'a.cinvcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('自由项1'),
      dataIndex: 'cfree1',
      key: 'a.cfree1',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('自由项2'),
      dataIndex: 'cfree2',
      key: 'a.cfree2',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('自由项3'),
      dataIndex: 'cfree3',
      key: 'a.cfree3',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑盘点商品明细'),
        onClick: handleForm.bind(this, { id: record.id }),
        auth: 'wh:pd:whPdStockPosBillsInv:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除盘点商品明细'),
        popConfirm: {
          title: t('是否确认删除盘点商品明细'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'wh:pd:whPdStockPosBillsInv:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerTable, { reload }] = useTable({
    api: whPdStockPosBillsInvListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { id: record.id };
    const res = await whPdStockPosBillsInvDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
