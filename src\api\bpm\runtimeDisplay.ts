/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BpmProcDef, BpmProcIns } from './model';

const { adminPath } = useGlobSetting();

export const bpmProcDefModel = (params?: BpmProcDef | any) =>
  defHttp.get<Recordable>({
    url: adminPath + '/bpm/display/app/rest/process-definitions/' + params.id + '/model-json',
  });

export const bpmProcInsModel = (params?: BpmProcIns | any) =>
  defHttp.get<Recordable>({
    url: adminPath + '/bpm/display/app/rest/process-instances/' + params.id + '/model-json',
  });

export const bpmProcInsHistoryModel = (params?: BpmProcIns | any) =>
  defHttp.get<Recordable>({
    url: adminPath + '/bpm/display/app/rest/process-instances/history/' + params.id + '/model-json',
  });

export const bpmProcInsTrace = (params?: BpmProcIns | any) =>
  defHttp.post<Recordable[]>({
    url: adminPath + '/bpm/display/app/rest/process-instances/' + params.id + '/trace-json',
  });
