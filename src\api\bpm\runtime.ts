/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BpmProcIns } from './model';

const { adminPath } = useGlobSetting();

export const bpmGetProcIns = (params?: BpmProcIns | any) =>
  defHttp.post<BpmProcIns>({ url: adminPath + '/bpm/bpmRuntime/getProcIns', params });

export const bpmStopProcess = (params?: BpmProcIns | any) =>
  defHttp.post<BpmProcIns>({ url: adminPath + '/bpm/bpmRuntime/stopProcess', params });

export const bpmTrace = (params?: BpmProcIns | any) =>
  defHttp.post<BpmProcIns>({ url: adminPath + '/bpm/bpmRuntime/trace', params });
