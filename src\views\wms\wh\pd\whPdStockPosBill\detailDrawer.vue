<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :title="getTitle"
    width="80%"
    @register="registerDrawer"
  >
    <BasicTable @register="registerDetailTable" />
  </BasicDrawer>
</template>

<script lang="ts" setup name="DetailDrawer">
  import { ref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicTable, useTable } from '/@/components/Table';
  import { whPdStockPosBillsInvListData } from '/@/api/wms/wh/pd/whPdStockPosBillsInv';



  const { t } = useI18n('wh.pd.whPdStockPosBill');
  const currentDetailId = ref('');

  const getTitle = computed(() => t('明细信息'));

  // 明细表格配置
  const [registerDetailTable, detailTable] = useTable({
    rowKey: 'id',
    pagination: {
      pageSize: 20,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    },
    bordered: true,
    size: 'small',
    inset: true,

    columns: [
      {
        title: t('单据号'),
        dataIndex: 'djno',
        width: 130,
        align: 'left',
      },
      {
        title: t('账存数'),
        dataIndex: 'iqty',
        width: 100,
        align: 'right',
        showSummary: true,
        showTotalSummary: true,
      },
      {
        title: t('实盘数'),
        dataIndex: 'frealqty',
        width: 100,
        align: 'right',
        showSummary: true,
        showTotalSummary: true,
      },
      {
        title: t('物料编码'),
        dataIndex: 'basInv.viewCode',
        width: 130,
        align: 'left',
      },
      {
        title: t('自由项1'),
        dataIndex: 'freeVO.cfree1',
        width: 100,
        align: 'left',
      },
      {
        title: t('自由项2'),
        dataIndex: 'freeVO.cfree2',
        width: 100,
        align: 'left',
      },
      {
        title: t('自由项3'),
        dataIndex: 'freeVO.cfree3',
        width: 100,
        align: 'left',
      },
    ],
    showCustomSummary: true,
    showTotalCustomSummary: true,
    onChange: handleDetailTableChange,
  });

  // 加载明细数据
  async function loadDetailData(hid: string, pageNo: number = 1, pageSize: number = 20) {
    try {
      const res = await whPdStockPosBillsInvListData({
        hid: hid,
        pageNo: pageNo,
        pageSize: pageSize,
      });

      detailTable.setTableData(res.list || []);
      detailTable.setPagination({
        total: res.count || res.total,
        current: pageNo,
        pageSize: pageSize,
      });
    } catch (error) {
      console.log('加载明细数据错误:', error);
    }
  }

  // 处理明细表格分页变化
  async function handleDetailTableChange(pagination: any) {
    await loadDetailData(currentDetailId.value, pagination.current, pagination.pageSize);
  }

  // 抽屉初始化
  const [registerDrawer, { setDrawerProps }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });

    if (data && data.id) {
      currentDetailId.value = data.id;
      await loadDetailData(data.id, 1, 20);
    }

    setDrawerProps({ loading: false });
  });
</script>
