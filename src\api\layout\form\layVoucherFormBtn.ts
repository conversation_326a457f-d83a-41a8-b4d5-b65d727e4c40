/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherFormBtn extends BasicModel<LayVoucherFormBtn> {
  viewCode?: string; // 布局标志
  btnType?: string; // 按钮类型
  auth?: string; // 权限
  icon?: string; // 图标
  title?: string; // 按钮标题
  btnId?: string; // 按钮ID
}

export const layVoucherFormBtnList = (params?: LayVoucherFormBtn | any) =>
  defHttp.get<LayVoucherFormBtn>({ url: adminPath + '/layout/formBtn/list', params });

export const layVoucherFormBtnListData = (params?: LayVoucherFormBtn | any) =>
  defHttp.post<Page<LayVoucherFormBtn>>({ url: adminPath + '/layout/formBtn/listData', params });

export const layVoucherFormBtnForm = (params?: LayVoucherFormBtn | any) =>
  defHttp.get<LayVoucherFormBtn>({ url: adminPath + '/layout/formBtn/form', params });

export const layVoucherFormBtnSave = (params?: any, data?: LayVoucherFormBtn | any) =>
  defHttp.postJson<LayVoucherFormBtn>({ url: adminPath + '/layout/formBtn/save', params, data });

export const layVoucherFormBtnDelete = (params?: LayVoucherFormBtn | any) =>
  defHttp.get<LayVoucherFormBtn>({ url: adminPath + '/layout/formBtn/delete', params });
