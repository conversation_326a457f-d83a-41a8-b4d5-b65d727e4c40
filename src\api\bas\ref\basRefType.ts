/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../model/baseModel';
import { UploadApiResult } from '../sys/upload';
import { UploadFileParams } from '/#/axios';

const { ctxPath, adminPath } = useGlobSetting();

export interface BasRefType extends TreeModel<BasRefType> {
  code?: string; // 功能编号
  name?: string; // 功能描述
}

export const basRefTypeList = (params?: BasRefType | any) =>
  defHttp.get<BasRefType>({ url: adminPath + '/bas/ref/basRefType/list', params });

export const basRefTypeListData = (params?: BasRefType | any) =>
  defHttp.post<BasRefType[]>({ url: adminPath + '/bas/ref/basRefType/listData', params });

export const basRefTypeForm = (params?: BasRefType | any) =>
  defHttp.get<BasRefType>({ url: adminPath + '/bas/ref/basRefType/form', params });

export const basRefTypeCreateNextNode = (params?: BasRefType | any) =>
  defHttp.get<BasRefType>({ url: adminPath + '/bas/ref/basRefType/createNextNode', params });

export const basRefTypeSave = (params?: any, data?: BasRefType | any) =>
  defHttp.postJson<BasRefType>({ url: adminPath + '/bas/ref/basRefType/save', params, data });

export const basRefTypeImportData = (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) =>
  defHttp.uploadFile<UploadApiResult>(
    {
      url: ctxPath + adminPath + '/bas/ref/basRefType/importData',
      onUploadProgress,
    },
    params,
  );

export const basRefTypeDelete = (params?: BasRefType | any) =>
  defHttp.get<BasRefType>({ url: adminPath + '/bas/ref/basRefType/delete', params });

export const basRefTypeTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/ref/basRefType/treeData', params });
