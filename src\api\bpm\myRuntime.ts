/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { Page } from '../model/baseModel';
import { BpmProcIns } from './model';

const { adminPath } = useGlobSetting();

export const bpmMyRuntimeList = (params?: BpmProcIns | any) =>
  defHttp.post<Page<BpmProcIns>>({ url: adminPath + '/bpm/bpmMyRuntime/listData', params });

export const bpmMyRuntimeForm = (params?: BpmProcIns | any) => {
  params.addPrefix = false;
  return defHttp.post<BpmProcIns>({ url: adminPath + '/bpm/bpmMyRuntime/form', params });
};
