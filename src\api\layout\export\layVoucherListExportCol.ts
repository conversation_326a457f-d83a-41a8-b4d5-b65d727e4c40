/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface LayVoucherListExportCol extends BasicModel<LayVoucherListExportCol> {
  code?: string; // 列表编码
  exAttrName?: string; // 字段名
  exTitle?: string; // 字段标题
  exType?: string; // 字段类型
  exAlign?: string; // 对齐方式
  exWidth?: number; // 导出列宽
  exWords?: number; // 字符个数
  exSort?: number; // 字段排序
  exColumn?: number; // 字段索引
  exDictType?: string; // 字典类型
  exFieldType?: string; // 反射类型
  exDataFormat?: string; // 数值格式
  groups?: string; // 字段归属
}

export const layVoucherListExportColList = (params?: LayVoucherListExportCol | any) =>
  defHttp.get<LayVoucherListExportCol>({ url: adminPath + '/layout/export/layVoucherListExportCol/list', params });

export const layVoucherListExportColListData = (params?: LayVoucherListExportCol | any) =>
  defHttp.post<Page<LayVoucherListExportCol>>({ url: adminPath + '/layout/export/layVoucherListExportCol/listData', params });

export const layVoucherListExportColForm = (params?: LayVoucherListExportCol | any) =>
  defHttp.get<LayVoucherListExportCol>({ url: adminPath + '/layout/export/layVoucherListExportCol/form', params });

export const layVoucherListExportColSave = (params?: any, data?: LayVoucherListExportCol | any) =>
  defHttp.postJson<LayVoucherListExportCol>({ url: adminPath + '/layout/export/layVoucherListExportCol/save', params, data });

export const layVoucherListExportColDelete = (params?: LayVoucherListExportCol | any) =>
  defHttp.get<LayVoucherListExportCol>({ url: adminPath + '/layout/export/layVoucherListExportCol/delete', params });
