/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface FilemanagerShared extends BasicModel<FilemanagerShared> {
  folderId?: string; // 文件夹编码
  fileUploadId?: string; // 文件上传编码
  fileName?: string; // 文件或文件夹名
  receiveUserCode?: string; // 接受者用户编码
  receiveUserName?: string; // 接收者用户名称
  clickNum?: number; // 点击次数
  ids?: string[]; // 主键批量处理
}

export const filemanagerSharedList = (params?: FilemanagerShared | any) =>
  defHttp.get<FilemanagerShared>({
    url: adminPath + '/filemanager/filemanagerShared/list',
    params,
  });

export const filemanagerSharedListData = (params?: FilemanagerShared | any) =>
  defHttp.post<Page<FilemanagerShared>>({
    url: adminPath + '/filemanager/filemanagerShared/listData',
    params,
  });

export const filemanagerSharedForm = (params?: FilemanagerShared | any) =>
  defHttp.get<FilemanagerShared>({
    url: adminPath + '/filemanager/filemanagerShared/form',
    params,
  });

export const filemanagerSharedView = (params?: FilemanagerShared | any) =>
  defHttp.get<FilemanagerShared>({
    url: adminPath + '/filemanager/filemanagerShared/view',
    params,
  });

export const filemanagerSharedSave = (params?: any, data?: FilemanagerShared | any) =>
  defHttp.postJson<FilemanagerShared>({
    url: adminPath + '/filemanager/filemanagerShared/save',
    params,
    data,
  });

export const filemanagerSharedDelete = (params?: FilemanagerShared | any) =>
  defHttp.get<FilemanagerShared>({
    url: adminPath + '/filemanager/filemanagerShared/delete',
    params,
  });
