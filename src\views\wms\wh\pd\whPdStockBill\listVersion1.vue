<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <!-- <a-button type="primary" @click="handleForm({})" v-auth="'wh:pd:whPdStockBill:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button> -->
        <a-button type="primary" @click="handleFormWh({})" v-auth="'wh:pd:whPdStock:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('按仓库/货位盘点') }}
        </a-button>
        <a-button type="default" @click="handleExport()">
          <Icon icon="i-ant-design:download-outlined" /> {{ t('导出') }}
        </a-button>
        <a-button
          type="dashed"
          success
          v-auth="'wh:pd:whPdStock:edit'"
          @click="handleGenerateTask2({})"
        >
          <Icon icon="i-ant-design:check-outlined" /> {{ t('生成盘盈盘亏') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ djno: record.djno })">
          {{ record.sourceNo }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
    <FormWh @register="registerDrawerWh" @success="handleSuccessWh" />
  </div>
</template>
<script lang="ts" setup name="ViewsWmsWhPdWhPdStockBillListVersion1">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { whPdStockBillDelete, whPdStockBillListData, createRdBill } from '/@/api/wms/wh/pd/whPdStockBill';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import { useGlobSetting } from '/@/hooks/setting';
  import { downloadByUrl } from '/@/utils/file/download';
  import InputForm from './form.vue';
  import FormWh from '/@/views/wms/wh/pd/whPdStockBill/formWh.vue';

  const { t } = useI18n('wh.pd.whPdStockBill');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('盘点单管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { lg: 6, md: 8 },
    labelWidth: 90,
    schemas: [
      // {
      //   label: t('来源id'),
      //   field: 'sourceId',
      //   component: 'Input',
      // },
      {
        label: t('来源单号'),
        field: 'sourceNo',
        component: 'Input',
      },
      {
        label: t('盘点方式'),
        field: 'pdType',
        component: 'Select',
        componentProps: {
          dictType: 'wms_pd_type',
          allowClear: true,
        },
      },
      {
        label: t('单据编号'),
        field: 'djno',
        component: 'Input',
      },
      {
        label: t('盘点状态'),
        field: 'pdStatus',
        component: 'Input',
        componentProps: {
          dictType: 'wms_pd_pdStatus',
          allowClear: true,
        },
      },
      {
        label: t('仓库'),
        field: 'whcode',
        component: 'Input',
      },
      {
        label: t('货位'),
        field: 'poscode',
        component: 'Input',
      },
      {
        label: t('制单人'),
        field: 'createBy',
        component: 'Input',
      },
      {
        label: t('备注信息'),
        field: 'remarks',
        component: 'Input',

      },
      {
        label: t('单据日期'),
        field: 'ddate',
        component: 'RangePicker',
        componentProps: {
          format: 'YYYY-MM-DD',
          // showTime: { format: 'HH:mm' },
        },
      },

      {
        label: t('总数'),
        field: 'iqty',
        component: 'Input',
      },
      {
        label: t('实盘总数'),
        field: 'sumqty',
        component: 'Input',
      },

      {
        label: t('公司编码'),
        field: 'companyCode',
        component: 'Input',
      },
      {
        label: t('公司名称'),
        field: 'companyname',
        component: 'Input',
      },
      {
        label: t('备注'),
        field: 'remarks',
        component: 'Input',
      },
    ],
    fieldMapToTime: [['ddate', ['ddate_gte', 'ddate_lte']]],
  };

  const tableColumns: BasicColumn[] = [
    // {
    //   title: t('明细id'),
    //   dataIndex: 'sourceDid',
    //   key: 'a.source_did',
    //   sorter: true,
    //   width: 230,
    //   align: 'left',
    // },
    {
      title: t('来源单号'),
      dataIndex: 'sourceNo',
      key: 'a.source_no',
      sorter: true,
      width: 130,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('单据编号'),
      dataIndex: 'djno',
      key: 'a.djno',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('盘点方式'),
      dataIndex: 'pdType',
      key: 'a.pd_type',
      sorter: true,
      width: 130,
      align: 'left',
      dictType: 'wms_pd_type',
    },
    {
      title: t('单据日期'),
      dataIndex: 'ddate',
      key: 'a.ddate',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('盘点状态'),
      dataIndex: 'pdStatus',
      key: 'a.pd_status',
      sorter: true,
      width: 130,
      align: 'left',
      dictType: 'wms_pd_pdStatus',
    },
    {
      title: t('总数'),
      dataIndex: 'iqty',
      key: 'a.iqty',
      sorter: true,
      width: 130,
      align: 'right',
      showSummary: true,
    },
    {
      title: t('实盘总数'),
      dataIndex: 'sumqty',
      key: 'a.sumqty',
      sorter: true,
      width: 130,
      align: 'right',
      showSummary: true,
    },
    {
      title: t('仓库'),
      dataIndex: 'whcode',
      key: 'a.whcode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('货位'),
      dataIndex: 'poscode',
      key: 'a.poscode',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('公司编码'),
      dataIndex: 'companyCode',
      key: 'a.company_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('公司名称'),
      dataIndex: 'companyname',
      key: 'a.companyname',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('修改时间'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑wh_pd_stock_bill'),
        onClick: handleForm.bind(this, { djno: record.djno }),
        auth: 'wh:pd:whPdStockBill:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除wh_pd_stock_bill'),
        popConfirm: {
          title: t('是否确认删除wh_pd_stock_bill'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'wh:pd:whPdStockBill:edit',
      },
    ],
  };

  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerDrawerWh, { openDrawer: openDrawerWh }] = useDrawer();
  const [registerTable, { reload, getSelectRowKeys, getForm }] = useTable({
    api: whPdStockBillListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    // actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
    rowSelection: {
      type: 'checkbox',
    },
    showCustomSummary: true,
  });

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  function handleFormWh(record: Recordable) {
    openDrawerWh(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { djno: record.djno };
    const res = await whPdStockBillDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }

  async function handleGenerateTask2() {
    let arr = await getSelectRowKeys();
    if (getSelectRowKeys().length != 1) {
      showMessage(t('请选择一行数据'));
      return;
    }
    const selIds = arr.join(',');
    const params = { djno: selIds };
    const res = await createRdBill(params);
    console.log(res, 'res');
    showMessage(res.message);
    handleSuccess({});
  }

  async function handleExport() {
    const { ctxAdminPath } = useGlobSetting();
    downloadByUrl({
      url: ctxAdminPath + '/wh/pd/whPdStockBill/exportData',
      params: getForm().getFieldsValue(),
    });
  }
</script>
