<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'wh:pd:whPdStock:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm">
      <template #queryButton>
        <div class="text-right">
          <a-button type="primary" @click="handleQuery">
            {{ t('查询') }}
          </a-button>
        </div>
      </template>
    </BasicForm>

    <!-- 查询结果表格 v-if="showQueryTable"-->
    <div class="mt-4">
      <h3 class="mb-2">{{ t('查询结果') }}</h3>
      <BasicTable
        @register="registerWhPdStocksTable"
        :canResize="false"
        :showTableSetting="false"
      />
    </div>
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsWhPdWhPdStockForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { WhPdStock, whPdStockSave, whPdStockForm, saveByPdType, subListData } from '/@/api/wms/wh/pd/whPdStock';
  import { companyTreeData } from '/@/api/sys/company';
  import { useUserStore } from '/@/store/modules/user';
  import { basWarehouseTreeData } from '/@/api/bas/house/basWarehouse';
  import { basPositionTreeData } from '/@/api/bas/pos/basPosition';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('wh.pd.whPdStock');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<WhPdStock>({} as WhPdStock);
  const showQueryTable = ref(false);
  const userStore = useUserStore();

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: (record.value as any).isNewRecord ? t('新增盘点单') : t('编辑盘点单'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('单据日期'),
      field: 'ddate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        // showTime: { format: 'HH:mm' },
      },
      required: true,
    },
    {
      label: t('单据号'),
      field: 'djno',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
      dynamicDisabled: true,
    },
    // {
    //   label: t('备注'),
    //   field: 'fexplanation',
    //   component: 'Input',
    //   componentProps: {
    //     maxlength: 255,
    //   },
    // },
    {
      label: t('总数'),
      field: 'iqty',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      dynamicDisabled: true,
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('实盘总数'),
      field: 'sumQty',
      component: 'Input',
      componentProps: {
        maxlength: 16,
      },
      dynamicDisabled: true,
      rules: [{ pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') }],
    },
    {
      label: t('公司名称'),
      field: 'companyCode',
      fieldLabel: 'companyName',
      component: 'TreeSelect',
      componentProps: {
        api: companyTreeData,
        allowClear: true,
        immediate: true,
      },
      required: true,
    },
    {
      label: t('公司编码'),
      field: 'companyCode',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      required: true,
      dynamicDisabled: true,
    },
  {
      label: t('盘点方式'),
      field: 'pdType',
      component: 'Select',
      componentProps: {
        maxlength: 64,
        dictType: 'wms_pd_type',
        allowClear: true,
      },
    },
    {
      label: t('仓库'),
      field: 'whCode',
      fieldLabel: 'basWare.cwhname',
      component: 'TreeSelect',
      componentProps: {
        api: basWarehouseTreeData,
        allowClear: true,
      },
      required: true,
    },
    {
      label: t('货位'),
      field: 'posCode',
      component: 'TreeSelect',
      componentProps: {
        api: basPositionTreeData,
        // params: { whcode: whCode.value },
        allowClear: true,
        canSelectParent: false,
        treeCheckable: true,
        immediate: true,
      },
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 500,
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      label: '',
      field: 'queryButton',
      component: 'Input',
      colProps: { lg: 24, md: 24 },
      slot: 'queryButton',
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { lg: 12, md: 24 },
  });

  const [registerWhPdStocksTable, whPdStocksTable] = useTable({
    // actionColumn: {
    //   width: 60,
    //   actions: (record: Recordable) => [
    //     {
    //       icon: 'i-ant-design:delete-outlined',
    //       color: 'error',
    //       popConfirm: {
    //         title: '是否确认删除',
    //         confirm: handleWhPdStocksDelete.bind(this, record),
    //       },
    //       auth: 'wh:pd:whPdStock:edit',
    //     },
    //   ],
    // },
    rowKey: 'id',
    pagination: true,
    bordered: true,
    size: 'small',
    inset: true,
  });

  async function setWhPdStocksTableData(_res: Recordable) {
    whPdStocksTable.setColumns([
      // {
      //   title: t('行号'),
      //   dataIndex: 'fentryid',
      //   width: 130,
      //   align: 'center',
      //   editRow: true,
      //   editComponent: 'Input',
      //   editComponentProps: {
      //     maxlength: 8,
      //   },
      //   editRule: false,
      // },
      {
        title: t('辅助属性'),
        dataIndex: 'f109',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
      {
        title: t('物料编码'),
        dataIndex: 'basInv.viewCode',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 64,
        },
        editRule: true,
      },
      {
        title: t('物料内码'),
        dataIndex: 'fitemid',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 64,
        },
        editRule: true,
      },
      {
        title: t('物料名称'),
        dataIndex: 'fitemname',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
      {
        title: t('仓库内码'),
        dataIndex: 'fstockid',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 64,
        },
        editRule: true,
      },
      {
        title: t('仓库编码'),
        dataIndex: 'fstockno',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 64,
        },
        editRule: true,
      },
      {
        title: t('仓库名称'),
        dataIndex: 'fstockname',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
      {
        title: t('账存数'),
        dataIndex: 'fplanqty',
        width: 130,
        align: 'right',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 16,
        },
        editRule: true,
      },
      {
        title: t('实存数'),
        dataIndex: 'frealqty',
        width: 130,
        align: 'right',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 16,
        },
        editRule: false,
      },
      {
        title: t('行备注'),
        dataIndex: 'fremarks',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
      {
        title: t('形象刊'),
        dataIndex: 'cfree1',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
      {
        title: t('自由项2'),
        dataIndex: 'cfree2',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
      {
        title: t('自由项3'),
        dataIndex: 'cfree3',
        width: 130,
        align: 'left',
        editRow: true,
        editComponent: 'Input',
        editComponentProps: {
          maxlength: 255,
        },
        editRule: false,
      },
    ]);
    // whPdStocksTable.setTableData(record.value.whPdStocksList || []);
    // 如果存在record.value.id，说明是编辑模式
    if (record?.value?.id) {
      // 调用接口subListData
      const res = await subListData({
        hid: {
          id: record.value?.id,
        },
      });
      // 把 res 赋值给 whPdStocksTable，whPdStocksTable表分页显示
      whPdStocksTable.setTableData(res.list || []);
      // 分页
      whPdStocksTable.setPagination({
        total: res.total,
        current: res.pageNo,
        pageSize: res.pageSize,
      });
    }
  }

  function handleWhPdStocksRowClick(record: Recordable) {
    record.onEdit?.(true, false);
  }

  // 查询函数
  async function handleQuery() {
    try {
      // 验证表单
      const formData = await validate();
      console.log('表单验证通过，查询参数:', formData);

      // 调用接口saveByPdType
      const resPdType = await saveByPdType(formData);

      if (resPdType.result === 'true') {
        // 查询
        const res = await subListData({
          hid: {
            id: resPdType.id,
          }
        });
        // 把 res 赋值给 whPdStocksTable，whPdStocksTable表分页显示
        whPdStocksTable.setTableData(res.list || []);
        // 分页
        whPdStocksTable.setPagination({
          total: res.total,
          current: res.pageNo,
          pageSize: res.pageSize,
        });
        // whPdStocksTable.setTableData(res.whPdStocksList || []);
      }
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('查询错误:', error);
    }
  }

  function handleWhPdStocksAdd() {
    whPdStocksTable.insertTableDataRecord({
      id: new Date().getTime(),
      isNewRecord: true,
      editable: true,
    });
  }

  function handleWhPdStocksDelete(record: Recordable) {
    whPdStocksTable.deleteTableDataRecord(record);
  }

  async function getWhPdStocksList() {
    let whPdStocksListValid = true;
    let whPdStocksList: Recordable[] = [];
    for (const record of whPdStocksTable.getDataSource()) {
      if (!(await record.onEdit?.(false, true))) {
        whPdStocksListValid = false;
      }
      whPdStocksList.push({
        ...record,
        id: !!record.isNewRecord ? '' : record.id,
      });
    }
    for (const record of whPdStocksTable.getDelDataSource()) {
      if (!!record.isNewRecord) continue;
      whPdStocksList.push({
        ...record,
        status: '1',
      });
    }
    if (!whPdStocksListValid) {
      throw { errorFields: [{ name: ['whPdStocksList'] }] };
    }
    return whPdStocksList;
  }

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await whPdStockForm(data);
    record.value = (res.whPdStock || {}) as WhPdStock;
    record.value.__t = new Date().getTime();

    // 如果是新建记录且没有设置单据日期，则设置为当天
    if ((record.value as any).isNewRecord && !record.value.ddate) {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      // const hours = String(now.getHours()).padStart(2, '0');
      // const minutes = String(now.getMinutes()).padStart(2, '0');
      record.value.ddate = `${year}-${month}-${day}`;
    }

    // 从userStore获取公司编码
    record.value.companyCode = userStore.getProjecte.code;
    record.value.companyName = userStore.getProjecte.name;

    setFieldsValue(record.value);
    setWhPdStocksTableData(res);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: (record.value as any).isNewRecord,
        id: (record.value as any).id,
      };
      data.whPdStocksList = await getWhPdStocksList();
      // console.log('submit', params, data, record);
      const res = await whPdStockSave(params, data);
      showMessage((res as any).message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
