/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { TreeDataModel, TreeModel } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface BasInvCls extends TreeModel<BasInvCls> {
  code?: string; // 分类编码
  name?: string; // 分类名称
}

export const basInvClsList = (params?: BasInvCls | any) =>
  defHttp.get<BasInvCls>({ url: adminPath + '/bas/inv/cls/list', params });

export const basInvClsListData = (params?: BasInvCls | any) =>
  defHttp.post<BasInvCls[]>({ url: adminPath + '/bas/inv/cls/listData', params });

export const basInvClsForm = (params?: BasInvCls | any) =>
  defHttp.get<BasInvCls>({ url: adminPath + '/bas/inv/cls/form', params });

export const basInvClsCreateNextNode = (params?: BasInvCls | any) =>
  defHttp.get<BasInvCls>({ url: adminPath + '/bas/inv/cls/createNextNode', params });

export const basInvClsSave = (params?: any, data?: BasInvCls | any) =>
  defHttp.postJson<BasInvCls>({ url: adminPath + '/bas/inv/cls/save', params, data });

export const basInvClsDelete = (params?: BasInvCls | any) =>
  defHttp.get<BasInvCls>({ url: adminPath + '/bas/inv/cls/delete', params });

export const basInvClsTreeData = (params?: any) =>
  defHttp.get<TreeDataModel[]>({ url: adminPath + '/bas/inv/cls/treeData', params });

export const updateFbStatus = (params?: any, data?: BasInvCls | any) =>
  defHttp.postJson<BasInvCls>({ url: adminPath + '/bas/inv/cls/updateFbStatus', params, data });
