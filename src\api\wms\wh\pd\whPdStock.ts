/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhPdStock extends BasicModel<WhPdStock> {
  fdate?: string; // 单据日期
  fbillno?: string; // 单据号
  fbiller?: string; // 制单人
  fexplanation?: string; // 备注
  iqty?: number; // 总数
  sumqty?: number; // 实盘总数
  bgen?: string; // 是否生成任务
  bsend?: string; // 是否推送
  senddate?: string; // 推送时间
  djStatus?: string; // 单据状态
  companyCode?: string; // 公司编码
  companyName?: string; // 公司名称
  whPdStocksList?: any[]; // 子表列表
}

export const whPdStockList = (params?: WhPdStock | any) =>
  defHttp.get<WhPdStock>({ url: adminPath + '/wh/pd/whPdStock/list', params });

export const whPdStockListData = (params?: WhPdStock | any) =>
  defHttp.post<Page<WhPdStock>>({ url: adminPath + '/wh/pd/whPdStock/listData', params });

export const whPdStockForm = (params?: WhPdStock | any) =>
  defHttp.get<WhPdStock>({ url: adminPath + '/wh/pd/whPdStock/form', params });

export const whPdStockSave = (params?: any, data?: WhPdStock | any) =>
  defHttp.postJson<WhPdStock>({ url: adminPath + '/wh/pd/whPdStock/save', params, data });

export const whPdStockDelete = (params?: WhPdStock | any) =>
  defHttp.get<WhPdStock>({ url: adminPath + '/wh/pd/whPdStock/delete', params });

export const synPdStock = (params?: WhPdStock | any) =>
  defHttp.post<Page<WhPdStock>>({ url: adminPath + '/wms/exter/syn/synPdStock', params });

export const createRdBill = (params?: WhPdStock | any) =>
  defHttp.post<Page<WhPdStock>>({ url: adminPath + '/wh/pd/whPdStock/createRdBill', params });

export const pushK3PdData = (params?: WhPdStock | any) =>
  defHttp.post<Page<WhPdStock>>({ url: adminPath + '/wh/pd/whPdStock/pushK3PdData', params });

// calBackk3PdData
export const calBackk3PdData = (params?: WhPdStock | any) =>
  defHttp.post<Page<WhPdStock>>({ url: adminPath + '/wh/pd/whPdStock/calBackk3PdData', params });

export const subListData = (params?: WhPdStock | any) =>
  defHttp.post<Page<WhPdStock>>({ url: adminPath + '/wh/pd/whPdStock/whPdStocksListData', params });

export const saveByPdType = (params?: WhPdStock | any) =>
  defHttp.post<Page<WhPdStock>>({ url: adminPath + '/wh/pd/whPdStock/saveByPdType', params });
