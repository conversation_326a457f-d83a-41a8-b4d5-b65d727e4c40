/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '../model/baseModel';

const { adminPath } = useGlobSetting();

export interface WhPdStockPosBillsInv extends BasicModel<WhPdStockPosBillsInv> {
  hid?: string; // hid
  djno?: string; // 单据号
  iqty?: number; // 账存数
  frealqty?: number; // 实盘数
  cinvcode?: string; // 物料编码
  cfree1?: string; // 自由项1
  cfree2?: string; // 自由项2
  cfree3?: string; // 自由项3
}

export const whPdStockPosBillsInvList = (params?: WhPdStockPosBillsInv | any) =>
  defHttp.get<WhPdStockPosBillsInv>({ url: adminPath + '/wh/pd/whPdStockPosBillsInv/list', params });

export const whPdStockPosBillsInvListData = (params?: WhPdStockPosBillsInv | any) =>
  defHttp.post<Page<WhPdStockPosBillsInv>>({ url: adminPath + '/wh/pd/whPdStockPosBillsInv/listData', params });

export const whPdStockPosBillsInvForm = (params?: WhPdStockPosBillsInv | any) =>
  defHttp.get<WhPdStockPosBillsInv>({ url: adminPath + '/wh/pd/whPdStockPosBillsInv/form', params });

export const whPdStockPosBillsInvSave = (params?: any, data?: WhPdStockPosBillsInv | any) =>
  defHttp.postJson<WhPdStockPosBillsInv>({ url: adminPath + '/wh/pd/whPdStockPosBillsInv/save', params, data });

export const whPdStockPosBillsInvDelete = (params?: WhPdStockPosBillsInv | any) =>
  defHttp.get<WhPdStockPosBillsInv>({ url: adminPath + '/wh/pd/whPdStockPosBillsInv/delete', params });

// /wh/pd/whPdStockPosBillsInv/listData

export const whPdStockPosBillsInvListData = (params?: WhPdStockPosBillsInv | any) =>
  defHttp.post<Page<WhPdStockPosBillsInv>>({ url: adminPath + '/wh/pd/whPdStockPosBillsInv/listData', params });
